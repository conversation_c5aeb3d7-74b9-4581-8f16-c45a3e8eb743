"use client"

import { useState, useEffect } from "react"
import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeftRight, DollarSign } from "lucide-react"
import Image from "next/image"

// Conversion rates (these would typically come from an API)
const CONVERSION_RATES = {
  RP_TO_USD: {
    650: 5,
    1380: 10,
    2800: 20,
    5000: 35,
    7200: 50,
    15000: 100
  },
  AS_TO_RP: 400, // 1 Ancient Spark = 400 RP
  ME_TO_RP: 125, // 1 Mythic Essence = 125 RP (approximate)
  RP_TO_BE: 6.5  // 1 RP ≈ 6.5 BE (approximate based on champion costs)
}

export default function ConversionToolPage() {
  const [rpAmount, setRpAmount] = useState("")
  const [usdAmount, setUsdAmount] = useState("")
  const [asAmount, setAsAmount] = useState("")
  const [rpFromAs, setRpFromAs] = useState("")
  const [meAmount, setMeAmount] = useState("")
  const [rpFromMe, setRpFromMe] = useState("")
  const [beAmount, setBEAmount] = useState("")
  const [rpFromBE, setRpFromBE] = useState("")

  // Calculate RP to USD conversion
  const calculateRpToUsd = (rp: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_USD).sort(([a], [b]) => Number(a) - Number(b))
    
    for (let i = 0; i < rates.length; i++) {
      const [rpTier, usdPrice] = rates[i]
      if (rp <= Number(rpTier)) {
        return (rp / Number(rpTier)) * usdPrice
      }
    }
    
    // For amounts larger than the highest tier, use the highest tier rate
    const [highestRp, highestUsd] = rates[rates.length - 1]
    return (rp / Number(highestRp)) * highestUsd
  }

  // Calculate USD to RP conversion
  const calculateUsdToRp = (usd: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_USD).sort(([a], [b]) => Number(b) - Number(a))
    
    for (let i = 0; i < rates.length; i++) {
      const [rpTier, usdPrice] = rates[i]
      if (usd >= usdPrice) {
        return Math.floor((usd / usdPrice) * Number(rpTier))
      }
    }
    
    // For amounts smaller than the smallest tier, use proportional calculation
    const [smallestRp, smallestUsd] = rates[rates.length - 1]
    return Math.floor((usd / smallestUsd) * Number(smallestRp))
  }

  // Handle RP input change
  const handleRpChange = (value: string) => {
    setRpAmount(value)
    if (value && !isNaN(Number(value))) {
      const usd = calculateRpToUsd(Number(value))
      setUsdAmount(usd.toFixed(2))
    } else {
      setUsdAmount("")
    }
  }

  // Handle USD input change
  const handleUsdChange = (value: string) => {
    setUsdAmount(value)
    if (value && !isNaN(Number(value))) {
      const rp = calculateUsdToRp(Number(value))
      setRpAmount(rp.toString())
    } else {
      setRpAmount("")
    }
  }

  // Handle Ancient Spark input change
  const handleAsChange = (value: string) => {
    setAsAmount(value)
    if (value && !isNaN(Number(value))) {
      const rp = Number(value) * CONVERSION_RATES.AS_TO_RP
      setRpFromAs(rp.toString())
    } else {
      setRpFromAs("")
    }
  }

  // Handle Mythic Essence input change
  const handleMeChange = (value: string) => {
    setMeAmount(value)
    if (value && !isNaN(Number(value))) {
      const rp = Number(value) * CONVERSION_RATES.ME_TO_RP
      setRpFromMe(rp.toString())
    } else {
      setRpFromMe("")
    }
  }

  // Handle Blue Essence input change
  const handleBEChange = (value: string) => {
    setBEAmount(value)
    if (value && !isNaN(Number(value))) {
      const rp = Math.round(Number(value) / CONVERSION_RATES.RP_TO_BE)
      setRpFromBE(rp.toString())
    } else {
      setRpFromBE("")
    }
  }

  const clearAll = () => {
    setRpAmount("")
    setUsdAmount("")
    setAsAmount("")
    setRpFromAs("")
    setMeAmount("")
    setRpFromMe("")
    setBEAmount("")
    setRpFromBE("")
  }

  return (
    <ConversionBackground customImageUrl="/images/SkinSalesCards.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Currency Conversion Tool
            </h1>
            <p className="text-gray-300 text-lg">
              Convert between League of Legends currencies and real money
            </p>
            <Badge variant="secondary" className="mt-2 bg-orange-600/20 text-orange-300 border-orange-600/30">
              Live Rates
            </Badge>
          </div>

          {/* Conversion Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
            
            {/* RP to USD Converter */}
            <Card className="bg-gray-900/40 border-orange-700/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-orange-400" />
                    <DollarSign className="h-5 w-5 text-green-400" />
                  </div>
                  Riot Points ↔ USD
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rp-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                        alt="RP"
                        width={16}
                        height={16}
                      />
                      Riot Points
                    </Label>
                    <Input
                      id="rp-input"
                      type="number"
                      placeholder="Enter RP amount"
                      value={rpAmount}
                      onChange={(e) => handleRpChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="usd-input" className="text-gray-300 flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-400" />
                      USD
                    </Label>
                    <Input
                      id="usd-input"
                      type="number"
                      step="0.01"
                      placeholder="Enter USD amount"
                      value={usdAmount}
                      onChange={(e) => handleUsdChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ancient Sparks to RP Converter */}
            <Card className="bg-gray-900/40 border-purple-700/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                      alt="Ancient Spark"
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-purple-400" />
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={20}
                      height={20}
                    />
                  </div>
                  Ancient Sparks → RP
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="as-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                        alt="Ancient Spark"
                        width={16}
                        height={16}
                      />
                      Ancient Sparks
                    </Label>
                    <Input
                      id="as-input"
                      type="number"
                      placeholder="Enter AS amount"
                      value={asAmount}
                      onChange={(e) => handleAsChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rp-from-as" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                        alt="RP"
                        width={16}
                        height={16}
                      />
                      Riot Points
                    </Label>
                    <Input
                      id="rp-from-as"
                      type="number"
                      placeholder="Converted RP"
                      value={rpFromAs}
                      readOnly
                      className="bg-gray-800/30 border-gray-700/30 text-gray-300 placeholder-gray-500"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  Rate: 1 Ancient Spark = 400 RP
                </div>
              </CardContent>
            </Card>

            {/* Mythic Essence to RP Converter */}
            <Card className="bg-gray-900/40 border-purple-700/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                      alt="Mythic Essence"
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-purple-400" />
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={20}
                      height={20}
                    />
                  </div>
                  Mythic Essence → RP
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="me-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                        alt="Mythic Essence"
                        width={16}
                        height={16}
                      />
                      Mythic Essence
                    </Label>
                    <Input
                      id="me-input"
                      type="number"
                      placeholder="Enter ME amount"
                      value={meAmount}
                      onChange={(e) => handleMeChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rp-from-me" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                        alt="RP"
                        width={16}
                        height={16}
                      />
                      Riot Points
                    </Label>
                    <Input
                      id="rp-from-me"
                      type="number"
                      placeholder="Converted RP"
                      value={rpFromMe}
                      readOnly
                      className="bg-gray-800/30 border-gray-700/30 text-gray-300 placeholder-gray-500"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  Rate: 1 Mythic Essence ≈ 125 RP
                </div>
              </CardContent>
            </Card>

            {/* Blue Essence to RP Converter */}
            <Card className="bg-gray-900/40 border-blue-700/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                      alt="Blue Essence"
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-blue-400" />
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={20}
                      height={20}
                    />
                  </div>
                  Blue Essence → RP
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="be-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                        alt="Blue Essence"
                        width={16}
                        height={16}
                      />
                      Blue Essence
                    </Label>
                    <Input
                      id="be-input"
                      type="number"
                      placeholder="Enter BE amount"
                      value={beAmount}
                      onChange={(e) => handleBEChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rp-from-be" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                        alt="RP"
                        width={16}
                        height={16}
                      />
                      Riot Points
                    </Label>
                    <Input
                      id="rp-from-be"
                      type="number"
                      placeholder="Converted RP"
                      value={rpFromBE}
                      readOnly
                      className="bg-gray-800/30 border-gray-700/30 text-gray-300 placeholder-gray-500"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  Rate: ~6.5 BE = 1 RP
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Clear All Button */}
          <div className="text-center mt-8">
            <Button 
              onClick={clearAll}
              variant="outline" 
              className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
            >
              Clear All
            </Button>
          </div>

        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
