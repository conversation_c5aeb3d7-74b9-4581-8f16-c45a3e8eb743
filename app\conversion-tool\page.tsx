"use client"

import { useState, useEffect } from "react"
import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeftRight, DollarSign, ChevronDown, Euro, Info } from "lucide-react"
import Image from "next/image"

// Conversion rates based on EU server pricing
const CONVERSION_RATES = {
  RP_TO_EUR: {
    575: 4.99,
    1380: 10.99,
    2800: 21.99,
    4500: 34.99,
    6500: 49.99,
    13500: 99.99,
    33500: 244.99,
    60200: 429.99
  },
  AS_TO_RP: 400, // 1 Ancient Spark = 400 RP
  ME_TO_RP: 125, // 1 Mythic Essence = 125 RP (approximate)
  RP_TO_BE: 6.5  // 1 RP ≈ 6.5 BE (approximate based on champion costs)
}

type ConversionType = 'rp-usd' | 'as-rp' | 'me-rp' | 'be-rp'

interface ConversionConfig {
  id: ConversionType
  label: string
  fromCurrency: {
    name: string
    icon: string
    placeholder: string
  }
  toCurrency: {
    name: string
    icon: string
    placeholder: string
  }
  rate: string
  bidirectional: boolean
}

const CONVERSION_CONFIGS: ConversionConfig[] = [
  {
    id: 'rp-usd',
    label: 'Riot Points ↔ EUR',
    fromCurrency: {
      name: 'Riot Points',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
      placeholder: 'Enter RP amount'
    },
    toCurrency: {
      name: 'EUR',
      icon: 'euro',
      placeholder: 'Enter EUR amount'
    },
    rate: 'Based on EU server pricing',
    bidirectional: true
  },
  {
    id: 'as-rp',
    label: 'Ancient Sparks → RP',
    fromCurrency: {
      name: 'Ancient Sparks',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg',
      placeholder: 'Enter AS amount'
    },
    toCurrency: {
      name: 'Riot Points',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
      placeholder: 'Converted RP'
    },
    rate: '1 Ancient Spark = 400 RP',
    bidirectional: false
  },
  {
    id: 'me-rp',
    label: 'Mythic Essence → RP',
    fromCurrency: {
      name: 'Mythic Essence',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg',
      placeholder: 'Enter ME amount'
    },
    toCurrency: {
      name: 'Riot Points',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
      placeholder: 'Converted RP'
    },
    rate: '1 Mythic Essence ≈ 125 RP',
    bidirectional: false
  },
  {
    id: 'be-rp',
    label: 'Blue Essence → RP',
    fromCurrency: {
      name: 'Blue Essence',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png',
      placeholder: 'Enter BE amount'
    },
    toCurrency: {
      name: 'Riot Points',
      icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
      placeholder: 'Converted RP'
    },
    rate: '~6.5 BE = 1 RP',
    bidirectional: false
  }
]

export default function ConversionToolPage() {
  const [selectedConversion, setSelectedConversion] = useState<ConversionType>('rp-usd')
  const [fromAmount, setFromAmount] = useState("")
  const [toAmount, setToAmount] = useState("")

  const currentConfig = CONVERSION_CONFIGS.find(config => config.id === selectedConversion)!

  // Calculate RP to EUR conversion
  const calculateRpToEur = (rp: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(a) - Number(b))

    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (rp <= Number(rpTier)) {
        return (rp / Number(rpTier)) * eurPrice
      }
    }

    // For amounts larger than the highest tier, use the highest tier rate
    const [highestRp, highestEur] = rates[rates.length - 1]
    return (rp / Number(highestRp)) * highestEur
  }

  // Calculate EUR to RP conversion
  const calculateEurToRp = (eur: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(b) - Number(a))

    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (eur >= eurPrice) {
        return Math.floor((eur / eurPrice) * Number(rpTier))
      }
    }

    // For amounts smaller than the smallest tier, use proportional calculation
    const [smallestRp, smallestEur] = rates[rates.length - 1]
    return Math.floor((eur / smallestEur) * Number(smallestRp))
  }

  // Handle conversion calculations
  const handleFromAmountChange = (value: string) => {
    setFromAmount(value)
    if (!value || isNaN(Number(value))) {
      setToAmount("")
      return
    }

    const numValue = Number(value)
    let result = 0

    switch (selectedConversion) {
      case 'rp-usd':
        result = calculateRpToEur(numValue)
        setToAmount(result.toFixed(2))
        break
      case 'as-rp':
        result = numValue * CONVERSION_RATES.AS_TO_RP
        setToAmount(result.toString())
        break
      case 'me-rp':
        result = numValue * CONVERSION_RATES.ME_TO_RP
        setToAmount(result.toString())
        break
      case 'be-rp':
        result = Math.round(numValue / CONVERSION_RATES.RP_TO_BE)
        setToAmount(result.toString())
        break
    }
  }

  // Handle reverse conversion (only for bidirectional)
  const handleToAmountChange = (value: string) => {
    if (!currentConfig.bidirectional) return

    setToAmount(value)
    if (!value || isNaN(Number(value))) {
      setFromAmount("")
      return
    }

    const numValue = Number(value)
    if (selectedConversion === 'rp-usd') {
      const result = calculateEurToRp(numValue)
      setFromAmount(result.toString())
    }
  }

  // Handle conversion type change
  const handleConversionChange = (newType: ConversionType) => {
    setSelectedConversion(newType)
    setFromAmount("")
    setToAmount("")
  }

  const clearAll = () => {
    setFromAmount("")
    setToAmount("")
  }

  return (
    <ConversionBackground customImageUrl="/images/SkinSalesCards.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Currency Conversion Tool
            </h1>
            <p className="text-gray-300 text-lg">
              Convert between League of Legends currencies and real money
            </p>
            <Badge variant="secondary" className="mt-2 bg-orange-600/20 text-orange-300 border-orange-600/30">
              Live Rates
            </Badge>
          </div>

          {/* Conversion Type Selector */}
          <div className="max-w-md mx-auto mb-8">
            <Label htmlFor="conversion-select" className="text-white text-lg font-medium mb-3 block">
              Select Conversion Type
            </Label>
            <Select value={selectedConversion} onValueChange={handleConversionChange}>
              <SelectTrigger className="bg-gray-900/60 border-orange-700/30 text-white backdrop-blur-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-orange-700/30">
                {CONVERSION_CONFIGS.map((config) => (
                  <SelectItem key={config.id} value={config.id} className="text-white hover:bg-gray-800">
                    {config.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Main Conversion Card */}
          <div className="max-w-2xl mx-auto">
            <Card className="bg-gray-900/60 border-orange-700/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {currentConfig.fromCurrency.icon === 'dollar' ? (
                      <DollarSign className="h-6 w-6 text-green-400" />
                    ) : currentConfig.fromCurrency.icon === 'euro' ? (
                      <Euro className="h-6 w-6 text-blue-400" />
                    ) : (
                      <Image
                        src={currentConfig.fromCurrency.icon}
                        alt={currentConfig.fromCurrency.name}
                        width={24}
                        height={24}
                      />
                    )}
                    <ArrowLeftRight className="h-5 w-5 text-orange-400" />
                    {currentConfig.toCurrency.icon === 'dollar' ? (
                      <DollarSign className="h-6 w-6 text-green-400" />
                    ) : currentConfig.toCurrency.icon === 'euro' ? (
                      <Euro className="h-6 w-6 text-blue-400" />
                    ) : (
                      <Image
                        src={currentConfig.toCurrency.icon}
                        alt={currentConfig.toCurrency.name}
                        width={24}
                        height={24}
                      />
                    )}
                  </div>
                  {currentConfig.label}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="from-input" className="text-gray-300 flex items-center gap-2">
                      {currentConfig.fromCurrency.icon === 'dollar' ? (
                        <DollarSign className="h-4 w-4 text-green-400" />
                      ) : currentConfig.fromCurrency.icon === 'euro' ? (
                        <Euro className="h-4 w-4 text-blue-400" />
                      ) : (
                        <Image
                          src={currentConfig.fromCurrency.icon}
                          alt={currentConfig.fromCurrency.name}
                          width={16}
                          height={16}
                        />
                      )}
                      {currentConfig.fromCurrency.name}
                    </Label>
                    <Input
                      id="from-input"
                      type="number"
                      step={selectedConversion === 'rp-usd' ? "0.01" : "1"}
                      placeholder={currentConfig.fromCurrency.placeholder}
                      value={fromAmount}
                      onChange={(e) => handleFromAmountChange(e.target.value)}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="to-input" className="text-gray-300 flex items-center gap-2">
                      {currentConfig.toCurrency.icon === 'dollar' ? (
                        <DollarSign className="h-4 w-4 text-green-400" />
                      ) : currentConfig.toCurrency.icon === 'euro' ? (
                        <Euro className="h-4 w-4 text-blue-400" />
                      ) : (
                        <Image
                          src={currentConfig.toCurrency.icon}
                          alt={currentConfig.toCurrency.name}
                          width={16}
                          height={16}
                        />
                      )}
                      {currentConfig.toCurrency.name}
                    </Label>
                    <Input
                      id="to-input"
                      type="number"
                      step={selectedConversion === 'rp-usd' ? "0.01" : "1"}
                      placeholder={currentConfig.toCurrency.placeholder}
                      value={toAmount}
                      onChange={(e) => handleToAmountChange(e.target.value)}
                      readOnly={!currentConfig.bidirectional}
                      className={`${
                        currentConfig.bidirectional
                          ? "bg-gray-800/50 border-gray-700/50 text-white"
                          : "bg-gray-800/30 border-gray-700/30 text-gray-300"
                      } placeholder-gray-400`}
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  {currentConfig.rate}
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Clear All Button */}
          <div className="text-center mt-8">
            <Button
              onClick={clearAll}
              variant="outline"
              className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
            >
              Clear All
            </Button>
          </div>

          {/* How Does This Work Section */}
          <div className="max-w-4xl mx-auto mt-12">
            <Card className="bg-gray-900/60 border-blue-700/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <Info className="h-6 w-6 text-blue-400" />
                  How Does This Work?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-gray-300">
                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    💰 Currency Conversion Rates
                  </p>
                  <p>
                    Our conversion tool uses <strong className="text-blue-400">official EU server pricing</strong> to provide accurate real-money values for League of Legends currencies. All RP to EUR conversions are based on Riot's current pricing tiers.
                  </p>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    🌍 Regional Support
                  </p>
                  <p>
                    Currently featuring <strong className="text-orange-400">EU server rates</strong>. We're working hard to add support for all regions including NA, EUNE, KR, JP, and more. <strong className="text-green-400">Multi-currency support coming very soon!</strong>
                  </p>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    ⚡ Other Currencies
                  </p>
                  <ul className="space-y-2 ml-4">
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Ancient Sparks:</strong> Premium currency for Exalted skins (1 AS = 400 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Mythic Essence:</strong> Used for prestige and mythic content (1 ME ≈ 125 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                      <strong>Blue Essence:</strong> Free currency for champions and upgrades (~6.5 BE = 1 RP)
                    </li>
                  </ul>
                </div>

                <div className="bg-orange-900/20 border border-orange-700/30 rounded-lg p-4 mt-6">
                  <p className="text-orange-300 font-medium flex items-center gap-2">
                    <span className="text-orange-400">🚀</span>
                    Coming Soon: USD, GBP, CAD, AUD, and 15+ more currencies!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
