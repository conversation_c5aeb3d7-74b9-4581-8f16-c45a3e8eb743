"use client"

import { useState } from "react"
import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeftRight, Euro, Info, ChevronsUpDown } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"

// Conversion rates based on EU server pricing
const CONVERSION_RATES = {
  RP_TO_EUR: {
    575: 4.99,
    1380: 10.99,
    2800: 21.99,
    4500: 34.99,
    6500: 49.99,
    13500: 99.99,
    33500: 244.99,
    60200: 429.99
  },
  AS_TO_RP: 400, // 1 Ancient Spark = 400 RP
  ME_TO_RP: 125, // 1 Mythic Essence = 125 RP (approximate)
  RP_TO_BE: 6.5, // 1 RP ≈ 6.5 BE (approximate based on champion costs)
  RP_TO_OE: 5.0  // 1 RP ≈ 5.0 OE (approximate based on skin shard costs)
}

// Real currency exchange rates (approximate, based on current market rates)
// These would typically come from a live exchange rate API
const CURRENCY_EXCHANGE_RATES = {
  EUR_TO_USD: 1.09,
  EUR_TO_GBP: 0.85,
  EUR_TO_JPY: 163.50,
  EUR_TO_CAD: 1.48,
  EUR_TO_AUD: 1.65,
  EUR_TO_CHF: 0.94,
  EUR_TO_SEK: 11.45,
  EUR_TO_NOK: 11.85,
  EUR_TO_DKK: 7.46,
  EUR_TO_PLN: 4.32,
  EUR_TO_CZK: 24.15,
  EUR_TO_HUF: 390.50,
  EUR_TO_RON: 4.97,
  EUR_TO_BGN: 1.96,
  EUR_TO_EGP: 53.75
}

// In-game currencies
interface InGameCurrency {
  id: string
  name: string
  icon: string
  placeholder: string
}

const IN_GAME_CURRENCIES: InGameCurrency[] = [
  {
    id: 'rp',
    name: 'Riot Points',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
    placeholder: 'Enter RP amount'
  },
  {
    id: 'as',
    name: 'Ancient Sparks',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg',
    placeholder: 'Enter AS amount'
  },
  {
    id: 'me',
    name: 'Mythic Essence',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg',
    placeholder: 'Enter ME amount'
  },
  {
    id: 'be',
    name: 'Blue Essence',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png',
    placeholder: 'Enter BE amount'
  },
  {
    id: 'oe',
    name: 'Orange Essence',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-loot/global/default/assets/tray_icons/currency_cosmetic.png',
    placeholder: 'Enter OE amount'
  }
]

// Real world currencies
interface RealCurrency {
  id: string
  name: string
  symbol: string
  icon: 'euro' | 'dollar' | 'pound' | 'yen' | 'generic'
  supported: boolean
}

const REAL_CURRENCIES: RealCurrency[] = [
  { id: 'eur', name: 'Euro', symbol: '€', icon: 'euro', supported: true },
  { id: 'usd', name: 'US Dollar', symbol: '$', icon: 'dollar', supported: true },
  { id: 'gbp', name: 'British Pound', symbol: '£', icon: 'pound', supported: true },
  { id: 'egp', name: 'Egyptian Pound', symbol: 'e£', icon: 'pound', supported: true },
  { id: 'jpy', name: 'Japanese Yen', symbol: '¥', icon: 'yen', supported: true },
  { id: 'cad', name: 'Canadian Dollar', symbol: 'C$', icon: 'dollar', supported: true },
  { id: 'aud', name: 'Australian Dollar', symbol: 'A$', icon: 'dollar', supported: true },
  { id: 'chf', name: 'Swiss Franc', symbol: 'CHF', icon: 'generic', supported: true },
  { id: 'sek', name: 'Swedish Krona', symbol: 'SEK', icon: 'generic', supported: true },
  { id: 'nok', name: 'Norwegian Krone', symbol: 'NOK', icon: 'generic', supported: true },
  { id: 'dkk', name: 'Danish Krone', symbol: 'DKK', icon: 'generic', supported: true },
  { id: 'pln', name: 'Polish Złoty', symbol: 'PLN', icon: 'generic', supported: true },
  { id: 'czk', name: 'Czech Koruna', symbol: 'CZK', icon: 'generic', supported: true },
  { id: 'huf', name: 'Hungarian Forint', symbol: 'HUF', icon: 'generic', supported: true },
  { id: 'ron', name: 'Romanian Leu', symbol: 'RON', icon: 'generic', supported: true },
  { id: 'bgn', name: 'Bulgarian Lev', symbol: 'BGN', icon: 'generic', supported: true }
]

// Server regions
interface ServerRegion {
  id: string
  name: string
  supported: boolean
}

const SERVER_REGIONS: ServerRegion[] = [
  { id: 'euw', name: 'Europe West', supported: true },
  { id: 'eune', name: 'Europe Nordic & East', supported: true },
  { id: 'na', name: 'North America', supported: false },
  { id: 'kr', name: 'Korea', supported: false },
  { id: 'jp', name: 'Japan', supported: false },
  { id: 'oce', name: 'Oceania', supported: false },
  { id: 'br', name: 'Brazil', supported: false },
  { id: 'lan', name: 'Latin America North', supported: false },
  { id: 'las', name: 'Latin America South', supported: false },
  { id: 'ru', name: 'Russia', supported: false },
  { id: 'tr', name: 'Turkey', supported: false },
  { id: 'sg', name: 'Singapore', supported: false },
  { id: 'ph', name: 'Philippines', supported: false },
  { id: 'tw', name: 'Taiwan', supported: false },
  { id: 'vn', name: 'Vietnam', supported: false },
  { id: 'th', name: 'Thailand', supported: false }
]

type ConversionMode = 'ingame-to-real' | 'ingame-to-ingame'

export default function ConversionToolPage() {
  const [conversionMode, setConversionMode] = useState<ConversionMode>('ingame-to-real')
  const [selectedInGameCurrency, setSelectedInGameCurrency] = useState('rp')
  const [selectedTargetInGameCurrency, setSelectedTargetInGameCurrency] = useState('as')
  const [selectedRealCurrency, setSelectedRealCurrency] = useState('eur')
  const [selectedServer, setSelectedServer] = useState('euw')
  const [inGameAmount, setInGameAmount] = useState("")
  const [targetAmount, setTargetAmount] = useState("")
  const [openInGame, setOpenInGame] = useState(false)
  const [openTargetInGame, setOpenTargetInGame] = useState(false)
  const [openReal, setOpenReal] = useState(false)
  const [openServer, setOpenServer] = useState(false)

  const currentInGameCurrency = IN_GAME_CURRENCIES.find(c => c.id === selectedInGameCurrency)!
  const currentTargetInGameCurrency = IN_GAME_CURRENCIES.find(c => c.id === selectedTargetInGameCurrency)!
  const currentRealCurrency = REAL_CURRENCIES.find(c => c.id === selectedRealCurrency)!
  const currentServer = SERVER_REGIONS.find(s => s.id === selectedServer)!

  // Calculate RP to EUR conversion
  const calculateRpToEur = (rp: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(a) - Number(b))

    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (rp <= Number(rpTier)) {
        return (rp / Number(rpTier)) * eurPrice
      }
    }

    // For amounts larger than the highest tier, use the highest tier rate
    const [highestRp, highestEur] = rates[rates.length - 1]
    return (rp / Number(highestRp)) * highestEur
  }

  // Calculate EUR to RP conversion
  const calculateEurToRp = (eur: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(b) - Number(a))

    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (eur >= eurPrice) {
        return Math.floor((eur / eurPrice) * Number(rpTier))
      }
    }

    // For amounts smaller than the smallest tier, use proportional calculation
    const [smallestRp, smallestEur] = rates[rates.length - 1]
    return Math.floor((eur / smallestEur) * Number(smallestRp))
  }

  // Calculate RP to any real currency conversion
  const calculateRpToRealCurrency = (rp: number, targetCurrency: string): number => {
    // First convert RP to EUR
    const eurAmount = calculateRpToEur(rp)

    // Then convert EUR to target currency
    const exchangeRateKey = `EUR_TO_${targetCurrency.toUpperCase()}` as keyof typeof CURRENCY_EXCHANGE_RATES
    const exchangeRate = CURRENCY_EXCHANGE_RATES[exchangeRateKey]

    if (!exchangeRate) return eurAmount // Fallback to EUR if rate not found

    return eurAmount * exchangeRate
  }

  // Calculate any real currency to RP conversion
  const calculateRealCurrencyToRp = (amount: number, sourceCurrency: string): number => {
    if (sourceCurrency === 'eur') {
      return calculateEurToRp(amount)
    }

    // Convert source currency to EUR first
    const exchangeRateKey = `EUR_TO_${sourceCurrency.toUpperCase()}` as keyof typeof CURRENCY_EXCHANGE_RATES
    const exchangeRate = CURRENCY_EXCHANGE_RATES[exchangeRateKey]

    if (!exchangeRate) return calculateEurToRp(amount) // Fallback to EUR if rate not found

    const eurAmount = amount / exchangeRate
    return calculateEurToRp(eurAmount)
  }

  // Get conversion rate info
  const getConversionInfo = () => {
    if (conversionMode === 'ingame-to-real') {
      if (selectedInGameCurrency === 'rp') {
        return {
          rate: currentRealCurrency.supported
            ? `Based on EU server pricing + ${currentRealCurrency.name} exchange rates`
            : `${currentRealCurrency.name} not yet supported`,
          bidirectional: true,
          supported: currentServer.supported && currentRealCurrency.supported
        }
      } else {
        return {
          rate: 'Real currency conversion only available for Riot Points',
          bidirectional: false,
          supported: false
        }
      }
    } else {
      // In-game to in-game conversions
      if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'as') {
        return {
          rate: '1 RP = 0.0025 Ancient Sparks (400 RP = 1 AS)',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'as' && selectedTargetInGameCurrency === 'rp') {
        return {
          rate: '1 Ancient Spark = 400 RP',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'me') {
        return {
          rate: '1 RP = 0.008 Mythic Essence (125 RP = 1 ME)',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'me' && selectedTargetInGameCurrency === 'rp') {
        return {
          rate: '1 Mythic Essence = 125 RP',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'be') {
        return {
          rate: '1 RP = 6.5 Blue Essence',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'be' && selectedTargetInGameCurrency === 'rp') {
        return {
          rate: '6.5 Blue Essence = 1 RP',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'oe') {
        return {
          rate: '1 RP = 5.0 Orange Essence',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === 'oe' && selectedTargetInGameCurrency === 'rp') {
        return {
          rate: '5.0 Orange Essence = 1 RP',
          bidirectional: true,
          supported: true
        }
      } else if (selectedInGameCurrency === selectedTargetInGameCurrency) {
        return {
          rate: 'Same currency selected',
          bidirectional: false,
          supported: false
        }
      } else {
        return {
          rate: 'Direct conversion not available',
          bidirectional: false,
          supported: false
        }
      }
    }
  }

  const conversionInfo = getConversionInfo()

  // Handle in-game currency amount change
  const handleInGameAmountChange = (value: string) => {
    setInGameAmount(value)
    if (!value || isNaN(Number(value)) || !conversionInfo.supported) {
      setTargetAmount("")
      return
    }

    const numValue = Number(value)
    let result = 0

    if (conversionMode === 'ingame-to-real') {
      if (selectedInGameCurrency === 'rp') {
        if (selectedRealCurrency === 'eur') {
          result = calculateRpToEur(numValue)
        } else {
          result = calculateRpToRealCurrency(numValue, selectedRealCurrency)
        }
        setTargetAmount(result.toFixed(2))
      }
    } else {
      // In-game to in-game conversions
      if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'as') {
        result = numValue / CONVERSION_RATES.AS_TO_RP
        setTargetAmount(result.toFixed(4))
      } else if (selectedInGameCurrency === 'as' && selectedTargetInGameCurrency === 'rp') {
        result = numValue * CONVERSION_RATES.AS_TO_RP
        setTargetAmount(result.toString())
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'me') {
        result = numValue / CONVERSION_RATES.ME_TO_RP
        setTargetAmount(result.toFixed(2))
      } else if (selectedInGameCurrency === 'me' && selectedTargetInGameCurrency === 'rp') {
        result = numValue * CONVERSION_RATES.ME_TO_RP
        setTargetAmount(result.toString())
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'be') {
        result = numValue * CONVERSION_RATES.RP_TO_BE
        setTargetAmount(result.toFixed(1))
      } else if (selectedInGameCurrency === 'be' && selectedTargetInGameCurrency === 'rp') {
        result = numValue / CONVERSION_RATES.RP_TO_BE
        setTargetAmount(result.toFixed(2))
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'oe') {
        result = numValue * CONVERSION_RATES.RP_TO_OE
        setTargetAmount(result.toFixed(1))
      } else if (selectedInGameCurrency === 'oe' && selectedTargetInGameCurrency === 'rp') {
        result = numValue / CONVERSION_RATES.RP_TO_OE
        setTargetAmount(result.toFixed(2))
      }
    }
  }

  // Handle target amount change (only for bidirectional)
  const handleTargetAmountChange = (value: string) => {
    if (!conversionInfo.bidirectional || !conversionInfo.supported) return

    setTargetAmount(value)
    if (!value || isNaN(Number(value))) {
      setInGameAmount("")
      return
    }

    const numValue = Number(value)

    if (conversionMode === 'ingame-to-real') {
      if (selectedInGameCurrency === 'rp') {
        let result: number
        if (selectedRealCurrency === 'eur') {
          result = calculateEurToRp(numValue)
        } else {
          result = calculateRealCurrencyToRp(numValue, selectedRealCurrency)
        }
        setInGameAmount(result.toString())
      }
    } else {
      // Reverse in-game to in-game conversions
      let result = 0
      if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'as') {
        result = numValue * CONVERSION_RATES.AS_TO_RP
        setInGameAmount(result.toString())
      } else if (selectedInGameCurrency === 'as' && selectedTargetInGameCurrency === 'rp') {
        result = numValue / CONVERSION_RATES.AS_TO_RP
        setInGameAmount(result.toFixed(4))
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'me') {
        result = numValue * CONVERSION_RATES.ME_TO_RP
        setInGameAmount(result.toString())
      } else if (selectedInGameCurrency === 'me' && selectedTargetInGameCurrency === 'rp') {
        result = numValue / CONVERSION_RATES.ME_TO_RP
        setInGameAmount(result.toFixed(2))
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'be') {
        result = numValue / CONVERSION_RATES.RP_TO_BE
        setInGameAmount(result.toFixed(2))
      } else if (selectedInGameCurrency === 'be' && selectedTargetInGameCurrency === 'rp') {
        result = numValue * CONVERSION_RATES.RP_TO_BE
        setInGameAmount(result.toFixed(1))
      } else if (selectedInGameCurrency === 'rp' && selectedTargetInGameCurrency === 'oe') {
        result = numValue / CONVERSION_RATES.RP_TO_OE
        setInGameAmount(result.toFixed(2))
      } else if (selectedInGameCurrency === 'oe' && selectedTargetInGameCurrency === 'rp') {
        result = numValue * CONVERSION_RATES.RP_TO_OE
        setInGameAmount(result.toFixed(1))
      }
    }
  }

  // Handle dropdown changes
  const handleInGameCurrencyChange = (currencyId: string) => {
    setSelectedInGameCurrency(currencyId)
    setInGameAmount("")
    setTargetAmount("")
    setOpenInGame(false)
  }

  const handleTargetInGameCurrencyChange = (currencyId: string) => {
    setSelectedTargetInGameCurrency(currencyId)
    setInGameAmount("")
    setTargetAmount("")
    setOpenTargetInGame(false)
  }

  const handleRealCurrencyChange = (currencyId: string) => {
    const currency = REAL_CURRENCIES.find(c => c.id === currencyId)
    if (currency?.supported) {
      setSelectedRealCurrency(currencyId)
      setInGameAmount("")
      setTargetAmount("")
    }
    setOpenReal(false)
  }

  const handleServerChange = (serverId: string) => {
    const server = SERVER_REGIONS.find(s => s.id === serverId)
    if (server?.supported) {
      setSelectedServer(serverId)
      setInGameAmount("")
      setTargetAmount("")
    }
    setOpenServer(false)
  }

  const handleModeChange = (mode: ConversionMode) => {
    setConversionMode(mode)
    setInGameAmount("")
    setTargetAmount("")
  }

  const clearAll = () => {
    setInGameAmount("")
    setTargetAmount("")
  }

  return (
    <ConversionBackground customImageUrl="/images/SkinSalesCards.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Currency Conversion Tool
            </h1>
            <p className="text-gray-300 text-lg">
              Convert between League of Legends currencies and real money
            </p>
            <Badge variant="secondary" className="mt-2 bg-orange-600/20 text-orange-300 border-orange-600/30">
              Live Rates
            </Badge>
          </div>

          {/* Conversion Mode Toggle */}
          <div className="max-w-md mx-auto mb-8">
            <div className="bg-gray-900/60 border border-orange-700/30 rounded-lg p-1 backdrop-blur-sm relative">
              {/* Animated Background Slider */}
              <div
                className={cn(
                  "absolute top-1 bottom-1 w-[calc(50%-2px)] bg-orange-600 rounded-md shadow-lg transition-all duration-300 ease-in-out",
                  conversionMode === 'ingame-to-real' ? "left-1" : "left-[calc(50%+1px)]"
                )}
              />

              <div className="grid grid-cols-2 gap-1 relative z-10">
                <button
                  onClick={() => handleModeChange('ingame-to-real')}
                  className={cn(
                    "px-4 py-3 rounded-md text-sm font-medium transition-all duration-300",
                    conversionMode === 'ingame-to-real'
                      ? "text-white"
                      : "text-gray-300 hover:text-white"
                  )}
                >
                  <div className="flex items-center justify-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={16}
                      height={16}
                    />
                    <ArrowLeftRight className="h-4 w-4" />
                    <Euro className="h-4 w-4" />
                  </div>
                  <div className="mt-1">Real Currency</div>
                </button>
                <button
                  onClick={() => handleModeChange('ingame-to-ingame')}
                  className={cn(
                    "px-4 py-3 rounded-md text-sm font-medium transition-all duration-300",
                    conversionMode === 'ingame-to-ingame'
                      ? "text-white"
                      : "text-gray-300 hover:text-white"
                  )}
                >
                  <div className="flex items-center justify-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={16}
                      height={16}
                    />
                    <ArrowLeftRight className="h-4 w-4" />
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                      alt="AS"
                      width={16}
                      height={16}
                    />
                  </div>
                  <div className="mt-1">In-Game Currency</div>
                </button>
              </div>
            </div>
          </div>

          {/* Currency Selection Dropdowns */}
          <div className="max-w-4xl mx-auto mb-8">
            <div className={cn(
              "grid gap-4",
              conversionMode === 'ingame-to-real'
                ? "grid-cols-1 md:grid-cols-3"
                : "grid-cols-1 md:grid-cols-2"
            )}>

              {/* In-Game Currency Dropdown */}
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">In-Game Currency</Label>
                <Popover open={openInGame} onOpenChange={setOpenInGame}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openInGame}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <div className="flex items-center gap-2">
                        <Image
                          src={currentInGameCurrency.icon}
                          alt={currentInGameCurrency.name}
                          width={16}
                          height={16}
                        />
                        <span className="truncate">{currentInGameCurrency.name}</span>
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      <CommandInput
                        placeholder="Search currencies..."
                        className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                      />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No currency found.
                        </CommandEmpty>
                        <CommandGroup>
                          {IN_GAME_CURRENCIES.map((currency) => (
                            <CommandItem
                              key={currency.id}
                              value={currency.id}
                              onSelect={() => handleInGameCurrencyChange(currency.id)}
                              className="text-white hover:bg-orange-400/10 cursor-pointer"
                            >
                              <div className="flex items-center gap-2 flex-1">
                                <Image
                                  src={currency.icon}
                                  alt={currency.name}
                                  width={16}
                                  height={16}
                                />
                                <span>{currency.name}</span>
                              </div>
                              <Check
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  selectedInGameCurrency === currency.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Real Currency Dropdown (only for ingame-to-real mode) */}
              {conversionMode === 'ingame-to-real' && (
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">Real Currency</Label>
                <Popover open={openReal} onOpenChange={setOpenReal}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openReal}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <div className="flex items-center gap-2">
                        {currentRealCurrency.icon === 'euro' && currentRealCurrency.supported ? (
                          <Euro className="h-4 w-4 text-blue-400" />
                        ) : (
                          <div className="w-4 h-4" /> // Empty space for alignment
                        )}
                        <span className="truncate">{currentRealCurrency.name}</span>
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      <CommandInput
                        placeholder="Search currencies..."
                        className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                      />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No currency found.
                        </CommandEmpty>
                        <CommandGroup>
                          {REAL_CURRENCIES.map((currency) => (
                            <CommandItem
                              key={currency.id}
                              value={currency.id}
                              onSelect={() => handleRealCurrencyChange(currency.id)}
                              className={cn(
                                "cursor-pointer",
                                currency.supported
                                  ? "text-white hover:bg-blue-400/10"
                                  : "text-gray-500 cursor-not-allowed opacity-50"
                              )}
                              disabled={!currency.supported}
                            >
                              <div className="flex items-center gap-2 flex-1">
                                {currency.icon === 'euro' && currency.supported ? (
                                  <Euro className="h-4 w-4 text-blue-400" />
                                ) : (
                                  <div className="w-4 h-4" /> // Empty space for alignment
                                )}
                                <span>{currency.name}</span>
                                {!currency.supported && (
                                  <span className="text-xs text-gray-500 ml-auto">Coming Soon</span>
                                )}
                              </div>
                              {currency.supported && (
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    selectedRealCurrency === currency.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              )}

              {/* Target In-Game Currency Dropdown (only for ingame-to-ingame mode) */}
              {conversionMode === 'ingame-to-ingame' && (
                <div className="space-y-2">
                  <Label className="text-white text-sm font-medium">Target Currency</Label>
                  <Popover open={openTargetInGame} onOpenChange={setOpenTargetInGame}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openTargetInGame}
                        className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                      >
                        <div className="flex items-center gap-2">
                          <Image
                            src={currentTargetInGameCurrency.icon}
                            alt={currentTargetInGameCurrency.name}
                            width={16}
                            height={16}
                          />
                          <span className="truncate">{currentTargetInGameCurrency.name}</span>
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                      <Command>
                        <CommandInput
                          placeholder="Search currencies..."
                          className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                        />
                        <CommandList className="max-h-[300px]">
                          <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                            No currency found.
                          </CommandEmpty>
                          <CommandGroup>
                            {IN_GAME_CURRENCIES.map((currency) => (
                              <CommandItem
                                key={currency.id}
                                value={currency.id}
                                onSelect={() => handleTargetInGameCurrencyChange(currency.id)}
                                className="text-white hover:bg-orange-400/10 cursor-pointer"
                              >
                                <div className="flex items-center gap-2 flex-1">
                                  <Image
                                    src={currency.icon}
                                    alt={currency.name}
                                    width={16}
                                    height={16}
                                  />
                                  <span>{currency.name}</span>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    selectedTargetInGameCurrency === currency.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              )}

              {/* Server Region Dropdown (only for ingame-to-real mode) */}
              {conversionMode === 'ingame-to-real' && (
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">Server Region</Label>
                <Popover open={openServer} onOpenChange={setOpenServer}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openServer}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <span className="truncate">{currentServer.name}</span>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      <CommandInput
                        placeholder="Search servers..."
                        className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                      />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No server found.
                        </CommandEmpty>
                        <CommandGroup>
                          {SERVER_REGIONS.map((server) => (
                            <CommandItem
                              key={server.id}
                              value={server.id}
                              onSelect={() => handleServerChange(server.id)}
                              className={cn(
                                "cursor-pointer",
                                server.supported
                                  ? "text-white hover:bg-green-400/10"
                                  : "text-gray-500 cursor-not-allowed opacity-50"
                              )}
                              disabled={!server.supported}
                            >
                              <div className="flex items-center justify-between w-full">
                                <span>{server.name}</span>
                                {!server.supported && (
                                  <span className="text-xs text-gray-500">Coming Soon</span>
                                )}
                                {server.supported && (
                                  <Check
                                    className={cn(
                                      "h-4 w-4",
                                      selectedServer === server.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                )}
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              )}

            </div>
          </div>

          {/* Main Conversion Card */}
          <div className="max-w-2xl mx-auto">
            <Card className={cn(
              "bg-gray-900/60 border-orange-700/30 backdrop-blur-sm",
              !conversionInfo.supported && "opacity-60"
            )}>
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src={currentInGameCurrency.icon}
                      alt={currentInGameCurrency.name}
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-orange-400" />
                    {conversionMode === 'ingame-to-real' ? (
                      currentRealCurrency.icon === 'euro' && currentRealCurrency.supported ? (
                        <Euro className="h-6 w-6 text-blue-400" />
                      ) : (
                        <span className="text-gray-400 text-lg font-bold">{currentRealCurrency.symbol}</span>
                      )
                    ) : (
                      <Image
                        src={currentTargetInGameCurrency.icon}
                        alt={currentTargetInGameCurrency.name}
                        width={24}
                        height={24}
                      />
                    )}
                  </div>
                  {currentInGameCurrency.name} {conversionInfo.bidirectional ? '↔' : '→'} {
                    conversionMode === 'ingame-to-real' ? currentRealCurrency.name : currentTargetInGameCurrency.name
                  }
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {!conversionInfo.supported && (
                  <div className="bg-amber-900/20 border border-amber-700/30 rounded-lg p-3 mb-4">
                    <p className="text-amber-300 text-sm flex items-center gap-2">
                      <Info className="h-4 w-4" />
                      This conversion combination is not yet supported. Coming soon!
                    </p>
                  </div>
                )}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ingame-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src={currentInGameCurrency.icon}
                        alt={currentInGameCurrency.name}
                        width={16}
                        height={16}
                      />
                      {currentInGameCurrency.name}
                    </Label>
                    <Input
                      id="ingame-input"
                      type="number"
                      step="1"
                      placeholder={currentInGameCurrency.placeholder}
                      value={inGameAmount}
                      onChange={(e) => handleInGameAmountChange(e.target.value)}
                      disabled={!conversionInfo.supported}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 disabled:opacity-50 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="target-input" className="text-gray-300 flex items-center gap-2">
                      {conversionMode === 'ingame-to-real' ? (
                        <>
                          {currentRealCurrency.icon === 'euro' && currentRealCurrency.supported ? (
                            <Euro className="h-4 w-4 text-blue-400" />
                          ) : (
                            <span className="text-gray-400 text-sm w-4">{currentRealCurrency.symbol}</span>
                          )}
                          {currentRealCurrency.name}
                        </>
                      ) : (
                        <>
                          <Image
                            src={currentTargetInGameCurrency.icon}
                            alt={currentTargetInGameCurrency.name}
                            width={16}
                            height={16}
                          />
                          {currentTargetInGameCurrency.name}
                        </>
                      )}
                    </Label>
                    <Input
                      id="target-input"
                      type="number"
                      step={conversionMode === 'ingame-to-real' ? "0.01" : "1"}
                      placeholder={
                        conversionMode === 'ingame-to-real'
                          ? `Enter ${currentRealCurrency.name} amount`
                          : currentTargetInGameCurrency.placeholder
                      }
                      value={targetAmount}
                      onChange={(e) => handleTargetAmountChange(e.target.value)}
                      readOnly={!conversionInfo.bidirectional}
                      disabled={!conversionInfo.supported}
                      className={cn(
                        "placeholder-gray-400 disabled:opacity-50 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]",
                        conversionInfo.bidirectional && conversionInfo.supported
                          ? "bg-gray-800/50 border-gray-700/50 text-white"
                          : "bg-gray-800/30 border-gray-700/30 text-gray-300"
                      )}
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  {conversionInfo.rate}
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Clear All Button */}
          <div className="text-center mt-8">
            <Button
              onClick={clearAll}
              variant="outline"
              className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50"
            >
              Clear All
            </Button>
          </div>

          {/* How Does This Work Section */}
          <div className="max-w-4xl mx-auto mt-12">
            <Card className="bg-gray-900/60 border-blue-700/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <Info className="h-6 w-6 text-blue-400" />
                  How Does This Work?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-gray-300">
                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    💰 Currency Conversion Rates
                  </p>
                  <p>
                    Our conversion tool uses <strong className="text-blue-400">official EU server pricing</strong> as the base for all calculations. RP to EUR conversions use Riot's current pricing tiers, and all other currencies are calculated using live exchange rates.
                  </p>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    🌍 Multi-Currency Support
                  </p>
                  <p>
                    <strong className="text-green-400">Now supporting 16 currencies!</strong> Including EUR, USD, GBP, JPY, CAD, AUD, CHF, SEK, NOK, DKK, PLN, CZK, HUF, RON, BGN, and EGP. All conversions are calculated using <strong className="text-orange-400">EU server RP pricing + current exchange rates</strong>.
                  </p>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    ⚡ Other Currencies
                  </p>
                  <ul className="space-y-2 ml-4">
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Ancient Sparks:</strong> Premium currency for Exalted skins (1 AS = 400 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Mythic Essence:</strong> Used for prestige and mythic content (1 ME ≈ 125 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                      <strong>Blue Essence:</strong> Free currency for champions and upgrades (~6.5 BE = 1 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                      <strong>Orange Essence:</strong> Currency for skin upgrades and crafting (~5.0 OE = 1 RP)
                    </li>
                  </ul>
                </div>

                <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-4 mt-6">
                  <p className="text-green-300 font-medium flex items-center gap-2">
                    <span className="text-green-400">✅</span>
                    Multi-Currency Support: 16 currencies now available! More regions coming soon.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
