"use client"

import React, { useEffect } from 'react'

interface ConversionBackgroundProps {
  customImageUrl?: string
  children: React.ReactNode
  blur?: number
  brightness?: number
  overlay?: string
}

export default function ConversionBackground({
  customImageUrl,
  children,
  blur = 10,
  brightness = 0.25,
  overlay = 'bg-gradient-to-b from-black/40 via-black/20 to-black/60'
}: ConversionBackgroundProps) {
  const backgroundUrl = customImageUrl || '/images/SkinSalesCards.jpg'

  useEffect(() => {
    if (backgroundUrl) {
      // Create background element with blur effect
      const backgroundElement = document.createElement('div')
      backgroundElement.id = 'conversion-background-image'
      backgroundElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
        background-image: url('${backgroundUrl}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(${blur}px) brightness(${brightness}) saturate(1.2);
        transform: scale(1.1);
        pointer-events: none;
      `
      document.body.insertBefore(backgroundElement, document.body.firstChild)

      // Add overlay
      const overlay = document.createElement('div')
      overlay.id = 'conversion-background-overlay'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to right, rgba(0,0,0,0.6), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to top, rgba(17, 24, 39, 0.9), transparent, rgba(17, 24, 39, 0.5));
        pointer-events: none;
      `
      document.body.insertBefore(overlay, document.body.firstChild)

      // Set header z-index for proper layering
      const header = document.querySelector('header')
      if (header instanceof HTMLElement) {
        header.style.zIndex = '50'
        header.style.position = 'relative'
      }

      // Set footer z-index for proper layering
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.zIndex = '50'
        footer.style.position = 'relative'
      }
    }

    // Cleanup function
    return () => {
      const backgroundElement = document.getElementById('conversion-background-image')
      const overlayElement = document.getElementById('conversion-background-overlay')
      
      if (backgroundElement) {
        backgroundElement.remove()
      }
      if (overlayElement) {
        overlayElement.remove()
      }

      // Reset header z-index
      const header = document.querySelector('header')
      if (header instanceof HTMLElement) {
        header.style.zIndex = ''
        header.style.position = ''
      }

      // Reset footer z-index
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.zIndex = ''
        footer.style.position = ''
      }
    }
  }, [backgroundUrl, blur, brightness])

  return (
    <div className="relative min-h-screen">
      {children}
    </div>
  )
}
